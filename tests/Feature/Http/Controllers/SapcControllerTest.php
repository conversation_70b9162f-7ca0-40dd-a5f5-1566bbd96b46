<?php

namespace Tests\Feature\Http\Controllers;

use App\Enums\Sapc\CertidaoPVLTipoModeloFormularioEnum;
use App\Enums\Sapc\StatusAnalise;
use App\Http\Controllers\SapcController;
use App\Models\Sapc\Analise;
use App\Models\Sapc\AnaliseFormulario;
use App\Models\Sapc\ModeloAnalise;
use App\Models\Sapc\ModeloAnaliseFormulario;
use App\Models\Sapc\ModeloFormulario;
use Tests\TestCase;

/**
 * @coversDefaultClass \App\Http\Controllers\SapcController
 */
class SapcControllerTest extends TestCase
{
    /**
     * @return array<string, ModeloFormulario>
     */
    private function createAnaliseWithForms(string $status): array
    {
        $modeloAnalise = ModeloAnalise::factory()->create();
        $analise = Analise::factory()->for($modeloAnalise)->create(['status' => $status]);

        $forms = [
            'conteudo' => $this->createForm($analise, CertidaoPVLTipoModeloFormularioEnum::CONTEUDO, false, false),
            'capa' => $this->createForm($analise, CertidaoPVLTipoModeloFormularioEnum::CONTEUDO, true, false),
            'conclusao' => $this->createForm($analise, CertidaoPVLTipoModeloFormularioEnum::CONTEUDO, false, true),
            'conclusivo' => $this->createForm($analise, CertidaoPVLTipoModeloFormularioEnum::CONCLUSIVO, false, false),
        ];

        return $forms;
    }

    private function createForm(Analise $analise, string $tipo, bool $capa, bool $conclusao): ModeloFormulario
    {
        $modeloAnalise = $analise->modeloAnalise;

        return ModeloFormulario::factory()
            ->has(ModeloAnaliseFormulario::factory()->for($modeloAnalise), 'modelosAnalisesFormularios')
            ->has(AnaliseFormulario::factory()->for($analise), 'analisesFormularios')
            ->create([
                'tipo_formulario' => $tipo,
                'capa' => $capa,
                'formulario_conclusao' => $conclusao,
            ]);
    }

    /**
     * @return list<mixed>
     */
    private function getFormularios(Analise $analise): array
    {
        $controller = new SapcController(app()->make(\App\Services\SapcService::class));
        $response = $controller->analiseFormularios($analise);

        return $response->collection->pluck('modelo_formulario_id')->toArray();
    }

    /**
     * @test
     *
     * @covers ::analiseFormularios
     * @covers \App\Services\SapcService::formulariosPorEtapaQuery
     */
    public function it_returns_only_content_forms_for_preliminary_analysis(): void
    {
        $forms = $this->createAnaliseWithForms(StatusAnalise::Iniciada);
        $analise = $forms['conteudo']->analisesFormularios()->first()->analise;
        $formIds = $this->getFormularios($analise);

        $expected = [
            $forms['conteudo']->id,
            $forms['capa']->id,
            $forms['conclusao']->id,
        ];
        $this->assertEqualsCanonicalizing($expected, $formIds);
    }

    /**
     * @test
     *
     * @covers ::analiseFormularios
     * @covers \App\Services\SapcService::formulariosPorEtapaQuery
     */
    public function it_returns_filtered_forms_for_conclusive_analysis(): void
    {
        $forms = $this->createAnaliseWithForms(StatusAnalise::ConclusivoIniciado);
        $analise = $forms['conteudo']->analisesFormularios()->first()->analise;
        $formIds = $this->getFormularios($analise);

        $expected = [
            $forms['conteudo']->id,
            $forms['conclusivo']->id,
        ];
        $this->assertEqualsCanonicalizing($expected, $formIds);
    }
}
